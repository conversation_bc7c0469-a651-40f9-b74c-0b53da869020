<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Aumentar límites para evitar timeouts
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');

// Log para debugging
error_log("=== INTELETGROUP_AJAX_DOCUMENTOS.PHP INICIADO ===");

// Iniciar sesión
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario_id']) || !isset($_SESSION['proyecto']) || $_SESSION['proyecto'] !== 'inteletGroup') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'No autorizado']);
    exit;
}

// Incluir archivos necesarios
require_once 'init.php';
require_once 'con_db.php';

// Verificar conexión a base de datos
if (!isset($mysqli)) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Error de conexión a la base de datos']);
    exit;
}

// Obtener acción solicitada
$action = $_GET['action'] ?? '';
$prospecto_id = isset($_GET['prospecto_id']) ? intval($_GET['prospecto_id']) : 0;

// Log de la petición
error_log("Acción solicitada: $action, Prospecto ID: $prospecto_id");

// Definir respuesta por defecto
$response = ['success' => false, 'error' => 'Acción no válida'];

switch ($action) {
    case 'getProspectoInfo':
        if ($prospecto_id > 0) {
            try {
                // Obtener información del prospecto
                $stmt = $mysqli->prepare("
                    SELECT 
                        p.id, p.tipo_persona, p.rut_cliente, p.razon_social, p.rubro,
                        p.email, p.telefono_celular, p.fecha_registro,
                        COALESCE(u.nombre_usuario, 'Sin asignar') as ejecutivo_nombre
                    FROM tb_inteletgroup_prospectos p
                    LEFT JOIN tb_experian_usuarios u ON p.usuario_id = u.id AND u.proyecto = 'inteletGroup'
                    WHERE p.id = ?
                ");
                
                if ($stmt) {
                    $stmt->bind_param("i", $prospecto_id);
                    $stmt->execute();
                    $stmt->bind_result($id, $tipo_persona, $rut_cliente, $razon_social, $rubro,
                                      $email, $telefono_celular, $fecha_registro, $ejecutivo_nombre);
                    
                    if ($stmt->fetch()) {
                        $response = [
                            'success' => true,
                            'prospecto' => [
                                'id' => $id,
                                'tipo_persona' => $tipo_persona,
                                'rut_cliente' => $rut_cliente,
                                'razon_social' => $razon_social,
                                'rubro' => $rubro,
                                'email' => $email,
                                'telefono_celular' => $telefono_celular,
                                'fecha_registro' => $fecha_registro,
                                'ejecutivo_nombre' => $ejecutivo_nombre
                            ]
                        ];
                    } else {
                        $response = ['success' => false, 'error' => 'Prospecto no encontrado'];
                    }
                    $stmt->close();
                } else {
                    $response = ['success' => false, 'error' => 'Error en la consulta'];
                    error_log("Error preparando consulta: " . $mysqli->error);
                }
            } catch (Exception $e) {
                $response = ['success' => false, 'error' => 'Error al obtener información del prospecto'];
                error_log("Excepción: " . $e->getMessage());
            }
        }
        break;

    case 'getDocumentosVisualizacion':
    case 'getDocumentosDescarga':
    case 'getChecklist':
        if ($prospecto_id > 0) {
            try {
                // Obtener documentos del prospecto
                $stmt = $mysqli->prepare("
                    SELECT 
                        d.id, d.tipo_documento_id, d.nombre_archivo, d.nombre_original,
                        d.tipo_archivo, d.tamaño_archivo, d.fecha_subida, d.ruta_archivo,
                        td.nombre as tipo_documento_nombre, td.codigo as tipo_documento_codigo
                    FROM tb_inteletgroup_documentos d
                    LEFT JOIN tb_inteletgroup_tipos_documento td ON d.tipo_documento_id = td.id
                    WHERE d.prospecto_id = ? AND d.estado = 'Activo'
                    ORDER BY d.fecha_subida DESC
                ");
                
                if ($stmt) {
                    $stmt->bind_param("i", $prospecto_id);
                    $stmt->execute();
                    $stmt->bind_result($doc_id, $tipo_doc_id, $nombre_archivo, $nombre_original,
                                     $tipo_archivo, $tamaño_archivo, $fecha_subida, $ruta_archivo,
                                     $tipo_doc_nombre, $tipo_doc_codigo);
                    
                    $documentos = [];
                    while ($stmt->fetch()) {
                        $documentos[] = [
                            'id' => $doc_id,
                            'tipo_documento_id' => $tipo_doc_id,
                            'nombre_archivo' => $nombre_archivo,
                            'nombre_original' => $nombre_original,
                            'tipo_archivo' => $tipo_archivo,
                            'tamaño_archivo' => $tamaño_archivo,
                            'fecha_subida' => $fecha_subida,
                            'tipo_documento_nombre' => $tipo_doc_nombre,
                            'tipo_documento_codigo' => $tipo_doc_codigo
                        ];
                    }
                    $stmt->close();
                    
                    // Si es getChecklist, necesitamos también obtener el checklist completo
                    if ($action === 'getChecklist') {
                        // Obtener información del prospecto para saber el tipo de persona
                        $stmt = $mysqli->prepare("SELECT tipo_persona FROM tb_inteletgroup_prospectos WHERE id = ?");
                        $stmt->bind_param("i", $prospecto_id);
                        $stmt->execute();
                        $stmt->bind_result($tipo_persona);
                        $stmt->fetch();
                        $stmt->close();
                        
                        // Obtener todos los tipos de documentos para este tipo de persona
                        $stmt = $mysqli->prepare("
                            SELECT id, codigo, nombre, descripcion, es_obligatorio
                            FROM tb_inteletgroup_tipos_documento
                            WHERE (tipo_persona = ? OR tipo_persona = 'Ambos') AND estado = 'Activo'
                            ORDER BY orden ASC
                        ");
                        
                        if ($stmt) {
                            $stmt->bind_param("s", $tipo_persona);
                            $stmt->execute();
                            $stmt->bind_result($tipo_id, $codigo, $nombre, $descripcion, $es_obligatorio);
                            
                            $checklist = [];
                            while ($stmt->fetch()) {
                                // Buscar si hay documento subido para este tipo
                                $documento_subido = null;
                                $estado = 'Pendiente';
                                
                                foreach ($documentos as $doc) {
                                    if ($doc['tipo_documento_id'] == $tipo_id) {
                                        $documento_subido = $doc;
                                        $estado = 'Subido';
                                        break;
                                    }
                                }
                                
                                $checklist[] = [
                                    'tipo_documento_id' => $tipo_id,
                                    'estado' => $estado,
                                    'codigo' => $codigo,
                                    'nombre' => $nombre,
                                    'descripcion' => $descripcion,
                                    'es_requerido' => $es_obligatorio,
                                    'documento_info' => $documento_subido
                                ];
                            }
                            $stmt->close();
                            
                            $response = [
                                'success' => true,
                                'checklist' => $checklist
                            ];
                        } else {
                            $response = ['success' => false, 'error' => 'Error al obtener checklist'];
                        }
                    } else {
                        $response = [
                            'success' => true,
                            'documentos' => $documentos
                        ];
                    }
                } else {
                    $response = ['success' => false, 'error' => 'Error en la consulta'];
                    error_log("Error preparando consulta: " . $mysqli->error);
                }
            } catch (Exception $e) {
                $response = ['success' => false, 'error' => 'Error al obtener documentos'];
                error_log("Excepción: " . $e->getMessage());
            }
        }
        break;

    case 'downloadAll':
        if ($prospecto_id > 0) {
            try {
                // Verificar si ZipArchive está disponible
                if (!class_exists('ZipArchive')) {
                    header('Content-Type: text/plain');
                    echo 'Error: La extensión ZipArchive no está disponible en el servidor.';
                    exit;
                }

                // Obtener información del prospecto para el nombre del archivo
                $stmt = $mysqli->prepare("SELECT razon_social FROM tb_inteletgroup_prospectos WHERE id = ?");
                $stmt->bind_param("i", $prospecto_id);
                $stmt->execute();
                $stmt->bind_result($razon_social);
                $stmt->fetch();
                $stmt->close();

                // Crear nombre seguro para el archivo
                $safe_name = preg_replace('/[^a-zA-Z0-9_-]/', '_', $razon_social);
                $zipFileName = 'documentos_' . $safe_name . '_' . date('Y-m-d_His') . '.zip';
                $zipPath = sys_get_temp_dir() . '/' . $zipFileName;

                // Verificar si hay documentos específicos seleccionados
                $selected_docs = $_GET['selected_docs'] ?? [];
                
                if (!empty($selected_docs) && is_array($selected_docs)) {
                    // Descargar solo documentos seleccionados
                    $placeholders = str_repeat('?,', count($selected_docs) - 1) . '?';
                    $stmt = $mysqli->prepare("
                        SELECT d.id, d.nombre_original, d.ruta_archivo
                        FROM tb_inteletgroup_documentos d
                        WHERE d.prospecto_id = ? AND d.id IN ($placeholders) AND d.estado = 'Activo'
                    ");
                    
                    if ($stmt) {
                        $types = 'i' . str_repeat('i', count($selected_docs));
                        $params = array_merge([$prospecto_id], $selected_docs);
                        $stmt->bind_param($types, ...$params);
                    }
                } else {
                    // Obtener todos los documentos del prospecto
                    $stmt = $mysqli->prepare("
                        SELECT d.id, d.nombre_original, d.ruta_archivo
                        FROM tb_inteletgroup_documentos d
                        WHERE d.prospecto_id = ? AND d.estado = 'Activo'
                    ");
                    
                    if ($stmt) {
                        $stmt->bind_param("i", $prospecto_id);
                    }
                }
                
                if ($stmt) {
                    $stmt->execute();
                    $stmt->bind_result($doc_id, $nombre_original, $ruta_archivo);
                    
                    $documentos = [];
                    while ($stmt->fetch()) {
                        $documentos[] = [
                            'id' => $doc_id,
                            'nombre_original' => $nombre_original,
                            'ruta_archivo' => $ruta_archivo
                        ];
                    }
                    $stmt->close();
                    
                    if (count($documentos) > 0) {
                        // Crear archivo ZIP
                        $zip = new ZipArchive();
                        if ($zip->open($zipPath, ZipArchive::CREATE) === TRUE) {
                            $added_files = 0;
                            
                            foreach ($documentos as $doc) {
                                if (file_exists($doc['ruta_archivo'])) {
                                    // Evitar nombres duplicados en el ZIP
                                    $file_name = $doc['nombre_original'];
                                    $counter = 1;
                                    while ($zip->locateName($file_name) !== false) {
                                        $path_parts = pathinfo($doc['nombre_original']);
                                        $file_name = $path_parts['filename'] . '_' . $counter . '.' . $path_parts['extension'];
                                        $counter++;
                                    }
                                    
                                    $zip->addFile($doc['ruta_archivo'], $file_name);
                                    $added_files++;
                                } else {
                                    error_log("Archivo no encontrado: " . $doc['ruta_archivo']);
                                }
                            }
                            $zip->close();
                            
                            if ($added_files > 0) {
                                // Enviar archivo ZIP
                                header('Content-Type: application/zip');
                                header('Content-Disposition: attachment; filename="' . $zipFileName . '"');
                                header('Content-Length: ' . filesize($zipPath));
                                header('Cache-Control: no-cache');
                                readfile($zipPath);
                                unlink($zipPath);
                                exit;
                            } else {
                                header('Content-Type: text/plain');
                                echo 'Error: No se encontraron archivos para descargar.';
                                exit;
                            }
                        } else {
                            header('Content-Type: text/plain');
                            echo 'Error: No se pudo crear el archivo ZIP.';
                            exit;
                        }
                    } else {
                        header('Content-Type: text/plain');
                        echo 'No hay documentos para descargar.';
                        exit;
                    }
                } else {
                    header('Content-Type: text/plain');
                    echo 'Error en la consulta de documentos.';
                    exit;
                }
            } catch (Exception $e) {
                header('Content-Type: text/plain');
                echo 'Error: ' . $e->getMessage();
                error_log("Excepción en downloadAll: " . $e->getMessage());
                exit;
            }
        }
        break;

    default:
        $response = ['success' => false, 'error' => 'Acción no reconocida'];
        break;
}

// Enviar respuesta JSON
header('Content-Type: application/json');
echo json_encode($response);
?>